using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class MessageDocumentationReportWindow : Window
    {
        private MessageDocumentation _documentation;

        public MessageDocumentationReportWindow()
        {
            InitializeComponent();
        }

        public MessageDocumentationReportWindow(MessageDocumentation documentation, List<MessageAttachment> attachments) : this()
        {
            _documentation = documentation;

            var reportData = new MessageDocumentationReportData
            {
                ReportNumber = documentation.ReportNumber,
                VisitNumber = documentation.VisitNumber,
                DocumentationDate = documentation.DocumentationDate,
                FirstOfficer = documentation.FirstOfficer ?? "غير محدد",
                SecondOfficer = documentation.SecondOfficer ?? "غير محدد",
                ThirdOfficer = documentation.ThirdOfficer ?? "غير محدد",
                Notes = documentation.Notes ?? "لا توجد ملاحظات",
                Attachments = attachments ?? new List<MessageAttachment>(),
                HasAttachments = attachments?.Count == 0
            };

            DataContext = reportData;
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    PrintSafely(printDialog);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة آمنة تتجنب مشاكل TypeConverter
        /// </summary>
        private void PrintSafely(System.Windows.Controls.PrintDialog printDialog)
        {
            try
            {
                // الطريقة الأولى: طباعة عادية
                var pageSize = new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);
                this.Measure(pageSize);
                this.Arrange(new Rect(pageSize));
                printDialog.PrintVisual(this, "توثيق الرسائل النصية - الصفحة الأولى");

                MessageBox.Show("تم إرسال الصفحة للطباعة بنجاح", "طباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex) when (ex.Message.Contains("TypeConverter") || ex.Message.Contains("MarkupExtension"))
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ TypeConverter، محاولة طباعة بديلة: {ex.Message}");
                try
                {
                    // الطريقة البديلة: إنشاء حاوي بسيط
                    var container = new Border
                    {
                        Background = Brushes.White,
                        Width = 794,
                        Height = 1123,
                        Child = this.Content as FrameworkElement
                    };

                    printDialog.PrintVisual(container, "توثيق الرسائل النصية - نسخة آمنة");
                    MessageBox.Show("تم إرسال الصفحة للطباعة بطريقة بديلة", "طباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشلت الطباعة البديلة: {ex2.Message}");
                    MessageBox.Show($"خطأ في الطباعة: {ex2.Message}\n\nجرب إعادة تشغيل البرنامج.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الانتقال إلى صفحة الصور
        /// </summary>
        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_documentation != null)
                {
                    var imagesWindow = new MessageDocumentationImagesReportWindow(_documentation);
                    imagesWindow.Show();
                }
                else
                {
                    MessageBox.Show("لا توجد بيانات توثيق متاحة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة الصور: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ التقرير كصورة
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "PNG Image|*.png|JPEG Image|*.jpg",
                    DefaultExt = "png",
                    FileName = $"توثيق_الرسائل_النصية_{_documentation?.VisitNumber}_{DateTime.Now:yyyyMMdd}.png"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    SaveAsImage(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصورة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ النافذة كصورة
        /// </summary>
        private void SaveAsImage(string filePath)
        {
            try
            {
                // تحديد حجم الصفحة (A4 بدقة 300 DPI)
                var pageSize = new Size(2480, 3508);

                // تحضير النافذة للحفظ
                this.Measure(pageSize);
                this.Arrange(new Rect(pageSize));

                // إنشاء RenderTargetBitmap
                var renderBitmap = new RenderTargetBitmap(
                    (int)pageSize.Width, (int)pageSize.Height, 300, 300, PixelFormats.Pbgra32);

                renderBitmap.Render(this);

                // حفظ الصورة
                BitmapEncoder encoder;
                if (Path.GetExtension(filePath).ToLower() == ".jpg")
                {
                    encoder = new JpegBitmapEncoder();
                }
                else
                {
                    encoder = new PngBitmapEncoder();
                }

                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    encoder.Save(fileStream);
                }

                MessageBox.Show($"تم حفظ التقرير بنجاح في:\n{filePath}", "حفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// نموذج بيانات تقرير التوثيق للعرض
    /// </summary>
    public class MessageDocumentationReportData
    {
        public string ReportNumber { get; set; }
        public string VisitNumber { get; set; }
        public System.DateTime DocumentationDate { get; set; }
        public string FirstOfficer { get; set; }
        public string SecondOfficer { get; set; }
        public string ThirdOfficer { get; set; }
        public string Notes { get; set; }
        public List<MessageAttachment> Attachments { get; set; }
        public bool HasAttachments { get; set; }
    }
}
