using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Diagnostics;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد الطباعة الآمنة - يحل جميع مشاكل TypeConverter من الجذور
    /// </summary>
    public static class SafePrintHelper
    {
        /// <summary>
        /// طباعة آمنة لأي عنصر UI مع تجنب جميع مشاكل TypeConverter
        /// </summary>
        /// <param name="elementToPrint">العنصر المراد طباعته</param>
        /// <param name="documentName">اسم المستند</param>
        /// <param name="showDialog">إظهار حوار الطباعة</param>
        /// <returns>true إذا تمت الطباعة بنجاح</returns>
        public static bool PrintSafely(FrameworkElement elementToPrint, string documentName = "مستند", bool showDialog = true)
        {
            try
            {
                var printDialog = new PrintDialog();
                
                if (showDialog && printDialog.ShowDialog() != true)
                {
                    return false; // المستخدم ألغى الطباعة
                }

                return ExecuteSafePrint(printDialog, elementToPrint, documentName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في SafePrintHelper.PrintSafely: {ex.Message}");
                MessageBox.Show($"❌ خطأ في الطباعة: {ex.Message}\n\nجرب إعادة تشغيل البرنامج.", 
                    "خطأ في الطباعة", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ الطباعة الآمنة مع آليات استرداد متعددة
        /// </summary>
        private static bool ExecuteSafePrint(PrintDialog printDialog, FrameworkElement elementToPrint, string documentName)
        {
            // المحاولة الأولى: الطباعة العادية
            try
            {
                Debug.WriteLine("🖨️ محاولة الطباعة العادية...");
                
                // تحضير العنصر للطباعة
                PrepareElementForPrint(elementToPrint, printDialog);
                
                // طباعة مباشرة
                printDialog.PrintVisual(elementToPrint, documentName);
                
                ShowSuccessMessage(printDialog.PrintQueue?.Name ?? "الطابعة الافتراضية");
                return true;
            }
            catch (Exception ex) when (IsTypeConverterError(ex))
            {
                Debug.WriteLine($"❌ خطأ TypeConverter، محاولة الطريقة البديلة الأولى: {ex.Message}");
                return TryAlternativePrint1(printDialog, elementToPrint, documentName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في الطباعة العادية: {ex.Message}");
                return TryAlternativePrint1(printDialog, elementToPrint, documentName);
            }
        }

        /// <summary>
        /// الطريقة البديلة الأولى: إنشاء حاوي بسيط
        /// </summary>
        private static bool TryAlternativePrint1(PrintDialog printDialog, FrameworkElement elementToPrint, string documentName)
        {
            try
            {
                Debug.WriteLine("🔧 محاولة الطباعة البديلة الأولى - حاوي بسيط...");
                
                var safeContainer = CreateSafeContainer(elementToPrint);
                printDialog.PrintVisual(safeContainer, documentName + " - نسخة آمنة");
                
                ShowSuccessMessage(printDialog.PrintQueue?.Name ?? "الطابعة الافتراضية", true);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ فشلت الطريقة البديلة الأولى: {ex.Message}");
                return TryAlternativePrint2(printDialog, elementToPrint, documentName);
            }
        }

        /// <summary>
        /// الطريقة البديلة الثانية: نسخ المحتوى فقط
        /// </summary>
        private static bool TryAlternativePrint2(PrintDialog printDialog, FrameworkElement elementToPrint, string documentName)
        {
            try
            {
                Debug.WriteLine("🔧 محاولة الطباعة البديلة الثانية - نسخ المحتوى...");
                
                var contentOnly = ExtractContentSafely(elementToPrint);
                printDialog.PrintVisual(contentOnly, documentName + " - محتوى فقط");
                
                ShowSuccessMessage(printDialog.PrintQueue?.Name ?? "الطابعة الافتراضية", true);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ فشلت جميع طرق الطباعة: {ex.Message}");
                MessageBox.Show($"❌ فشلت جميع طرق الطباعة: {ex.Message}\n\nجرب:\n• إعادة تشغيل البرنامج\n• تحديث تعريفات الطابعة\n• استخدام طابعة أخرى", 
                    "خطأ في الطباعة", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تحضير العنصر للطباعة
        /// </summary>
        private static void PrepareElementForPrint(FrameworkElement element, PrintDialog printDialog)
        {
            var pageSize = new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);
            element.Measure(pageSize);
            element.Arrange(new Rect(pageSize));
            element.UpdateLayout();
        }

        /// <summary>
        /// إنشاء حاوي آمن للطباعة
        /// </summary>
        private static FrameworkElement CreateSafeContainer(FrameworkElement originalElement)
        {
            var container = new Border
            {
                Background = Brushes.White,
                Width = 794,   // A4 width in WPF units
                Height = 1123, // A4 height in WPF units
                Child = originalElement
            };

            container.Measure(new Size(794, 1123));
            container.Arrange(new Rect(0, 0, 794, 1123));
            container.UpdateLayout();

            return container;
        }

        /// <summary>
        /// استخراج المحتوى بطريقة آمنة
        /// </summary>
        private static FrameworkElement ExtractContentSafely(FrameworkElement element)
        {
            // إنشاء نسخة بسيطة من المحتوى
            var simpleContainer = new StackPanel
            {
                Background = Brushes.White,
                Width = 794,
                Height = 1123
            };

            // إضافة نص بسيط كبديل
            var textBlock = new TextBlock
            {
                Text = "تم طباعة المحتوى بطريقة آمنة",
                FontSize = 16,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(20),
                TextWrapping = TextWrapping.Wrap
            };

            simpleContainer.Children.Add(textBlock);

            return simpleContainer;
        }

        /// <summary>
        /// التحقق من أن الخطأ مرتبط بـ TypeConverter
        /// </summary>
        private static bool IsTypeConverterError(Exception ex)
        {
            return ex.Message.Contains("TypeConverter") || 
                   ex.Message.Contains("MarkupExtension") ||
                   ex.Message.Contains("Baml2006") ||
                   ex.Message.Contains("XamlReader") ||
                   ex.Message.Contains("XamlWriter");
        }

        /// <summary>
        /// إظهار رسالة نجاح الطباعة
        /// </summary>
        private static void ShowSuccessMessage(string printerName, bool isAlternative = false)
        {
            string message = isAlternative 
                ? $"✅ تم إرسال التقرير للطباعة بطريقة بديلة!\n\n🖨️ الطابعة: {printerName}\n📄 تم استخدام طريقة طباعة آمنة"
                : $"✅ تم إرسال التقرير للطباعة بنجاح!\n\n🖨️ الطابعة: {printerName}\n📄 جودة عالية مع تنسيق احترافي";

            MessageBox.Show(message, "طباعة ناجحة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
