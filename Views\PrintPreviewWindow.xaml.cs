using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using System.Printing;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Windows.Markup;
using PdfParagraph = iTextSharp.text.Paragraph;

namespace DriverManagementSystem.Views
{
    public partial class PrintPreviewWindow : Window
    {
        private FrameworkElement _reportContent;

        public PrintPreviewWindow(FrameworkElement reportContent)
        {
            InitializeComponent();
            _reportContent = reportContent;
            LoadPreview();
        }

        private void LoadPreview()
        {
            try
            {
                // Clone the report content for preview
                var clonedContent = CloneReportContent(_reportContent);
                ReportContentPresenter.Content = clonedContent;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعاينة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FrameworkElement CloneReportContent(FrameworkElement original)
        {
            // Preview dimensions - Full size for better visibility
            double previewWidth = 780;   // Larger width to prevent cropping
            double previewHeight = 1080; // Full height

            // Create a new ReportView for preview
            var clonedReport = new ReportView
            {
                DataContext = original.DataContext,
                Width = previewWidth,
                Height = previewHeight,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            // Force layout update for preview
            clonedReport.Measure(new Size(previewWidth, previewHeight));
            clonedReport.Arrange(new Rect(0, 0, previewWidth, previewHeight));
            clonedReport.UpdateLayout();

            return clonedReport;
        }

        private void PrintSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowPrintSettingsDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعدادات الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SavePdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveAsPdf();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void PrintReport()
        {
            try
            {
                PrintDialog printDialog = new PrintDialog();

                // تحسين إعدادات الطباعة
                if (printDialog.PrintTicket != null)
                {
                    // تحديد مقاس A4
                    printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                        System.Printing.PageMediaSizeName.ISOA4);

                    // تحديد الاتجاه (عمودي)
                    printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                    // تحديد جودة الطباعة
                    printDialog.PrintTicket.OutputQuality = System.Printing.OutputQuality.High;
                }

                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء نسخة محسنة للطباعة
                    var printVersion = CreatePrintVersion();

                    // طباعة مع مواصفات A4 محسنة
                    printDialog.PrintVisual(printVersion, "تقرير النظام - A4");

                    MessageBox.Show("✅ تم إرسال التقرير للطباعة بنجاح!\n\n📐 المقاس: A4 (210×297 مم)\n🖨️ الطابعة: " + printDialog.PrintQueue.Name +
                        "\n📄 جودة عالية مع تنسيق احترافي", "طباعة ناجحة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                // معالجة خاصة لخطأ TypeConverter
                string errorMessage = ex.Message;
                if (ex.Message.Contains("TypeConverter") || ex.Message.Contains("MarkupExtension"))
                {
                    errorMessage = "خطأ في تحويل البيانات للطباعة. سيتم استخدام طريقة طباعة بديلة.";

                    // محاولة طباعة بطريقة بديلة
                    try
                    {
                        var printDialog = new PrintDialog();
                        printDialog.PrintVisual(_reportContent, "تقرير النظام - نسخة بديلة");
                        MessageBox.Show("✅ تم إرسال التقرير للطباعة بطريقة بديلة!", "طباعة ناجحة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }
                    catch
                    {
                        // إذا فشلت الطريقة البديلة أيضاً
                    }
                }

                MessageBox.Show($"❌ خطأ في الطباعة: {errorMessage}\n\nتأكد من:\n• تشغيل الطابعة\n• توفر الورق\n• صحة إعدادات الطابعة\n\nإذا استمرت المشكلة، جرب إعادة تشغيل البرنامج.", "خطأ في الطباعة",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FrameworkElement CreatePrintVersion()
        {
            try
            {
                // A4 exact dimensions for printing (300 DPI for high quality)
                double a4Width = 2480;   // 8.27 inches × 300 DPI
                double a4Height = 3508;  // 11.69 inches × 300 DPI

                // Scale down for WPF (96 DPI)
                double wpfWidth = 794;   // 8.27 inches × 96 DPI
                double wpfHeight = 1123; // 11.69 inches × 96 DPI

                // Create optimized container for printing
                var printContainer = new Border
                {
                    Background = Brushes.White,
                    Width = wpfWidth,
                    Height = wpfHeight,
                    BorderThickness = new Thickness(0),
                    Padding = new Thickness(20), // هوامش مناسبة
                    UseLayoutRounding = true,
                    SnapsToDevicePixels = true
                };

                // استخدام المحتوى الأصلي مباشرة لتجنب مشاكل TypeConverter
                printContainer.Child = _reportContent;

                // Force proper layout for printing
                printContainer.Measure(new Size(wpfWidth, wpfHeight));
                printContainer.Arrange(new Rect(0, 0, wpfWidth, wpfHeight));
                printContainer.UpdateLayout();

                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating print version: {ex.Message}");
                return CreateFallbackPrintVersion();
            }
        }

        private FrameworkElement CloneAndOptimizeContent(FrameworkElement original)
        {
            try
            {
                // إنشاء نسخة محسنة من المحتوى
                var cloned = CloneReportContent(original);

                // تحسين العناصر للطباعة
                OptimizeElementForPrinting(cloned);

                return cloned;
            }
            catch
            {
                return original;
            }
        }

        private void OptimizeElementForPrinting(DependencyObject element)
        {
            try
            {
                if (element is TextBlock textBlock)
                {
                    // تحسين النصوص للطباعة
                    textBlock.UseLayoutRounding = true;
                    textBlock.SnapsToDevicePixels = true;
                    TextOptions.SetTextFormattingMode(textBlock, TextFormattingMode.Display);
                    TextOptions.SetTextRenderingMode(textBlock, TextRenderingMode.ClearType);

                    // ضمان حد أدنى لحجم الخط
                    if (textBlock.FontSize < 8)
                        textBlock.FontSize = 8;
                }
                else if (element is Border border)
                {
                    border.UseLayoutRounding = true;
                    border.SnapsToDevicePixels = true;
                }
                else if (element is Panel panel)
                {
                    panel.UseLayoutRounding = true;
                    panel.SnapsToDevicePixels = true;
                }

                // تطبيق التحسينات على العناصر الفرعية
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
                {
                    var child = VisualTreeHelper.GetChild(element, i);
                    OptimizeElementForPrinting(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error optimizing element: {ex.Message}");
            }
        }

        private FrameworkElement CreateFallbackPrintVersion()
        {
            try
            {
                var container = new Border
                {
                    Background = Brushes.White,
                    Width = 794,
                    Height = 1123,
                    Child = _reportContent
                };

                container.Measure(new Size(794, 1123));
                container.Arrange(new Rect(0, 0, 794, 1123));
                return container;
            }
            catch
            {
                return _reportContent;
            }
        }

        private void SaveAsPdf()
        {
            SaveFileDialog saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files (*.pdf)|*.pdf",
                DefaultExt = "pdf",
                FileName = $"محضر_استخراج_عروض_الأسعار_{DateTime.Now:yyyy-MM-dd_HH-mm}.pdf"
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    // Create full-size version for PDF
                    var pdfVersion = CreatePrintVersion();
                    CreatePdfFromVisual(pdfVersion, saveDialog.FileName);

                    MessageBox.Show($"تم حفظ PDF بمقاس A4 بنجاح في:\n{saveDialog.FileName}\n📐 المقاس: 210×297 مم", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // Ask if user wants to open the file
                    var result = MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        public void CreatePdfFromVisual(FrameworkElement visual, string fileName)
        {
            try
            {
                // Always use image-based PDF to capture exactly what's on screen
                CreateImageBasedPdf(visual, fileName);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateTextBasedPdf(iTextSharp.text.Document document, FrameworkElement visual)
        {
            // Get the report data
            var reportData = visual.DataContext as ViewModels.ReportViewModel;
            if (reportData?.ReportData == null) return;

            var data = reportData.ReportData;

            // Arabic font setup
            string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "arial.ttf");
            if (!File.Exists(fontPath))
            {
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
            }

            BaseFont arabicFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
            var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
            var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);
            var smallFont = new iTextSharp.text.Font(arabicFont, 10, iTextSharp.text.Font.NORMAL);

            // Header section
            var headerTable = new PdfPTable(2);
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 1, 1 });

            var dateCell = new PdfPCell(new Phrase($"التاريخ: {data.ReportDate}", normalFont));
            dateCell.Border = Rectangle.NO_BORDER;
            dateCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            headerTable.AddCell(dateCell);

            var visitCell = new PdfPCell(new Phrase($"رقم الزيارة: {data.VisitNumber}", normalFont));
            visitCell.Border = Rectangle.NO_BORDER;
            visitCell.HorizontalAlignment = Element.ALIGN_LEFT;
            headerTable.AddCell(visitCell);

            document.Add(headerTable);
            document.Add(new PdfParagraph(" "));

            // Organization info
            var orgPara = new PdfParagraph("الجمهورية اليمنية", headerFont);
            orgPara.Alignment = Element.ALIGN_CENTER;
            document.Add(orgPara);

            var deptPara = new PdfParagraph("المجلس المحلي للمديرية", normalFont);
            deptPara.Alignment = Element.ALIGN_CENTER;
            document.Add(deptPara);

            var branchPara = new PdfParagraph("فرع عدن والمحافظات", normalFont);
            branchPara.Alignment = Element.ALIGN_CENTER;
            document.Add(branchPara);

            document.Add(new PdfParagraph(" "));

            // Title
            var titlePara = new PdfParagraph("محضر استخراج عروض أسعار", titleFont);
            titlePara.Alignment = Element.ALIGN_CENTER;
            titlePara.SpacingAfter = 20;
            document.Add(titlePara);

            // Projects section
            var projectsTitle = new PdfParagraph("المشاريع التي سيتم زيارتها", headerFont);
            projectsTitle.SpacingBefore = 10;
            document.Add(projectsTitle);

            if (data.Projects != null && data.Projects.Count > 0)
            {
                foreach (var project in data.Projects)
                {
                    var projectPara = new PdfParagraph($"{project.SerialNumber} - {project.ProjectNumber} - {project.ProjectName}", normalFont);
                    projectPara.IndentationLeft = 20;
                    document.Add(projectPara);
                }
            }

            document.Add(new PdfParagraph(" "));

            // Technical data section
            var techTitle = new PdfParagraph("البيانات الفنية", headerFont);
            document.Add(techTitle);

            document.Add(new PdfParagraph($"طبيعة التشغيل: {data.VisitNature}", normalFont));
            document.Add(new PdfParagraph($"القائم بالزيارة: {data.VisitConductor}", normalFont));
            document.Add(new PdfParagraph($"خط السير: {data.RouteDescription}", normalFont));

            document.Add(new PdfParagraph(" "));

            // Price offers table
            var offersTitle = new PdfParagraph("قائمة الأسعار المقدمة من السائقين", headerFont);
            document.Add(offersTitle);

            if (data.PriceOffers != null && data.PriceOffers.Count > 0)
            {
                var offersTable = new PdfPTable(4);
                offersTable.WidthPercentage = 100;
                offersTable.SetWidths(new float[] { 1, 3, 2, 2 });

                // Headers
                offersTable.AddCell(new PdfPCell(new Phrase("الرقم", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("اسم السائق", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("رقم التلفون", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("المبلغ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

                // Data
                foreach (var offer in data.PriceOffers)
                {
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.SerialNumber.ToString(), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.DriverName, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.PhoneNumber, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.OfferedPrice.ToString("N0"), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                }

                document.Add(offersTable);
            }

            document.Add(new PdfParagraph(" "));

            // Duration section
            var durationTable = new PdfPTable(3);
            durationTable.WidthPercentage = 100;

            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ النزول: {data.DepartureDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ العودة: {data.ReturnDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"عدد الأيام: {data.DaysCount}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            document.Add(durationTable);
            document.Add(new PdfParagraph(" "));

            // Selected driver section
            var driverTitle = new PdfParagraph("السائق المختار وبيانات السيارة", headerFont);
            document.Add(driverTitle);

            var driverTable = new PdfPTable(2);
            driverTable.WidthPercentage = 100;

            driverTable.AddCell(new PdfPCell(new Phrase($"السائق: {data.SelectedDriverName}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"سنة الصنع: {data.VehicleModel}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"التلفون: {data.SelectedDriverPhone}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"اللون: {data.VehicleColor}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"نوع السيارة: {data.VehicleType}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"رقم اللوحة: {data.PlateNumber}", normalFont)) { Border = Rectangle.NO_BORDER });

            document.Add(driverTable);
            document.Add(new Paragraph(" "));

            // Signatures section
            var signaturesTable = new PdfPTable(3);
            signaturesTable.WidthPercentage = 100;
            signaturesTable.SpacingBefore = 30;

            signaturesTable.AddCell(new PdfPCell(new Phrase("المكلف بالمهمة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("مسئول الحركة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("يعتمد", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.TaskManagerName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.MovementResponsibleName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.BranchManagerName}\n{data.BranchManagerTitle}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });

            document.Add(signaturesTable);
        }

        private void CreateImageBasedPdf(FrameworkElement visual, string fileName)
        {
            // Create PDF with exact A4 dimensions (210x297mm)
            var document = new iTextSharp.text.Document(PageSize.A4, 0, 0, 0, 0); // No margins for full page

            using (var stream = new FileStream(fileName, FileMode.Create))
            {
                var writer = PdfWriter.GetInstance(document, stream);
                document.Open();

                // A4 dimensions at 300 DPI for high quality print
                // A4 = 210×297mm = 8.27×11.69 inches
                // At 300 DPI: 2480×3508 pixels
                int a4Width300dpi = 2480;
                int a4Height300dpi = 3508;

                // Force layout at proper A4 proportions (using 96 DPI base)
                double a4WidthAt96dpi = 794;  // 8.27 inches × 96 DPI
                double a4HeightAt96dpi = 1123; // 11.69 inches × 96 DPI

                visual.Measure(new Size(a4WidthAt96dpi, a4HeightAt96dpi));
                visual.Arrange(new Rect(0, 0, a4WidthAt96dpi, a4HeightAt96dpi));
                visual.UpdateLayout();

                // Create high-resolution bitmap at 300 DPI
                var renderBitmap = new RenderTargetBitmap(
                    a4Width300dpi, a4Height300dpi,
                    300, 300, // 300 DPI for print quality
                    PixelFormats.Pbgra32);

                // Render with proper scaling to 300 DPI
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // White background for full A4 page
                    drawingContext.DrawRectangle(Brushes.White, null,
                        new Rect(0, 0, a4Width300dpi, a4Height300dpi));

                    // Scale from 96 DPI to 300 DPI (300/96 = 3.125)
                    double scale = 300.0 / 96.0;
                    drawingContext.PushTransform(new ScaleTransform(scale, scale));

                    var visualBrush = new VisualBrush(visual)
                    {
                        Stretch = Stretch.Fill, // Fill the entire page
                        AlignmentX = AlignmentX.Left,
                        AlignmentY = AlignmentY.Top
                    };

                    drawingContext.DrawRectangle(visualBrush, null,
                        new Rect(0, 0, a4WidthAt96dpi, a4HeightAt96dpi));
                    drawingContext.Pop();
                }

                renderBitmap.Render(drawingVisual);

                // Use PNG for lossless quality
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    var imageBytes = memoryStream.ToArray();

                    var image = iTextSharp.text.Image.GetInstance(imageBytes);

                    // Scale to fill the entire A4 page exactly
                    image.ScaleToFit(PageSize.A4.Width, PageSize.A4.Height);
                    image.SetAbsolutePosition(0, 0); // Position at bottom-left corner

                    document.Add(image);
                }

                document.Close();
            }
        }

        private void ShowPrintSettingsDialog()
        {
            try
            {
                PrintDialog printDialog = new PrintDialog();

                // تحسين إعدادات الطباعة المتقدمة
                if (printDialog.PrintTicket != null)
                {
                    // تحديد مقاس A4
                    printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                        System.Printing.PageMediaSizeName.ISOA4);

                    // تحديد الاتجاه (عمودي)
                    printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                    // تحديد جودة الطباعة العالية
                    printDialog.PrintTicket.OutputQuality = System.Printing.OutputQuality.High;

                    // تحديد نوع الورق
                    printDialog.PrintTicket.PageMediaType = System.Printing.PageMediaType.Plain;

                    // تحديد دقة الطباعة
                    printDialog.PrintTicket.PageResolution = new System.Printing.PageResolution(600, 600);
                }

                // عرض مربع حوار إعدادات الطباعة
                if (printDialog.ShowDialog() == true)
                {
                    // عرض معلومات الطابعة المختارة
                    var printerInfo = $"✅ تم تحديد إعدادات الطباعة بنجاح!\n\n" +
                                    $"🖨️ الطابعة: {printDialog.PrintQueue.Name}\n" +
                                    $"📐 المقاس: A4 (210×297 مم)\n" +
                                    $"📄 الاتجاه: عمودي\n" +
                                    $"🎯 الجودة: عالية (600 DPI)\n" +
                                    $"📋 نوع الورق: عادي\n\n" +
                                    $"هل تريد الطباعة الآن بهذه الإعدادات؟";

                    var result = MessageBox.Show(printerInfo, "إعدادات الطباعة",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // طباعة مباشرة بالإعدادات المحددة
                        PrintWithSettings(printDialog);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إعدادات الطباعة: {ex.Message}\n\nتأكد من:\n• تشغيل خدمة الطباعة\n• وجود طابعات مثبتة\n• صحة تعريفات الطابعة",
                    "خطأ في الإعدادات", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintWithSettings(PrintDialog printDialog)
        {
            try
            {
                // إنشاء نسخة محسنة للطباعة
                var printVersion = CreatePrintVersion();

                // طباعة مع الإعدادات المحددة
                printDialog.PrintVisual(printVersion, "تقرير النظام - طباعة احترافية");

                MessageBox.Show("✅ تم إرسال التقرير للطباعة بنجاح!\n\n" +
                    $"🖨️ الطابعة: {printDialog.PrintQueue.Name}\n" +
                    $"📄 تم إرسال المستند للطباعة بجودة عالية\n" +
                    $"⏱️ يرجى انتظار اكتمال الطباعة",
                    "طباعة ناجحة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة مع الإعدادات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                // معالجة خاصة لخطأ TypeConverter
                if (ex.Message.Contains("TypeConverter") || ex.Message.Contains("MarkupExtension"))
                {
                    try
                    {
                        // محاولة طباعة بطريقة بديلة
                        printDialog.PrintVisual(_reportContent, "تقرير النظام - نسخة بديلة");
                        MessageBox.Show("✅ تم إرسال التقرير للطباعة بطريقة بديلة!", "طباعة ناجحة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }
                    catch
                    {
                        // إذا فشلت الطريقة البديلة أيضاً
                    }
                }

                MessageBox.Show($"❌ خطأ في الطباعة: {ex.Message}\n\nتأكد من:\n• تشغيل الطابعة\n• توفر الورق\n• عدم وجود انحشار ورق\n\nإذا استمرت المشكلة، جرب إعادة تشغيل البرنامج.",
                    "خطأ في الطباعة", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
