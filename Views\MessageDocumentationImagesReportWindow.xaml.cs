using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عرض الصور المرفقة في توثيق الرسائل النصية
    /// </summary>
    public partial class MessageDocumentationImagesReportWindow : Window
    {
        private MessageDocumentation _documentation;

        public MessageDocumentationImagesReportWindow(MessageDocumentation documentation)
        {
            InitializeComponent();
            _documentation = documentation;
            LoadDocumentationData();
            LoadImages();
        }

        /// <summary>
        /// تحميل بيانات التوثيق الأساسية
        /// </summary>
        private void LoadDocumentationData()
        {
            try
            {
                DataContext = new
                {
                    ReportNumber = _documentation?.ReportNumber ?? "غير محدد",
                    VisitNumber = _documentation?.VisitNumber ?? "غير محدد"
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات التوثيق: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الصور المحفوظة
        /// </summary>
        private void LoadImages()
        {
            try
            {
                // تحميل الصورة الأولى
                LoadImage(_documentation?.ImagePath1, Image1, Image1Label, EmptyState1, "الصورة الأولى");
                
                // تحميل الصورة الثانية
                LoadImage(_documentation?.ImagePath2, Image2, Image2Label, EmptyState2, "الصورة الثانية");
                
                // تحميل الصورة الثالثة
                LoadImage(_documentation?.ImagePath3, Image3, Image3Label, EmptyState3, "الصورة الثالثة");
                
                // تحميل الصورة الرابعة
                LoadImage(_documentation?.ImagePath4, Image4, Image4Label, EmptyState4, "الصورة الرابعة");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصور: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل صورة واحدة
        /// </summary>
        private void LoadImage(string imagePath, System.Windows.Controls.Image imageControl, 
            System.Windows.Controls.TextBlock labelControl, System.Windows.Controls.StackPanel emptyStateControl, 
            string defaultLabel)
        {
            try
            {
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    // تحميل الصورة
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    
                    imageControl.Source = bitmap;
                    imageControl.Visibility = Visibility.Visible;
                    
                    // تحديث التسمية بناءً على اسم الملف
                    var fileName = Path.GetFileNameWithoutExtension(imagePath);
                    labelControl.Text = !string.IsNullOrEmpty(fileName) ? fileName : defaultLabel;
                    labelControl.Visibility = Visibility.Visible;
                    
                    // إخفاء حالة الفراغ
                    emptyStateControl.Visibility = Visibility.Collapsed;
                }
                else
                {
                    // إظهار حالة الفراغ
                    imageControl.Visibility = Visibility.Collapsed;
                    labelControl.Visibility = Visibility.Collapsed;
                    emptyStateControl.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الصورة {imagePath}: {ex.Message}");
                
                // إظهار حالة الفراغ في حالة الخطأ
                imageControl.Visibility = Visibility.Collapsed;
                labelControl.Visibility = Visibility.Collapsed;
                emptyStateControl.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// طباعة الصفحة
        /// </summary>
        public void PrintPage()
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    PrintSafely(printDialog);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة آمنة تتجنب مشاكل TypeConverter
        /// </summary>
        private void PrintSafely(System.Windows.Controls.PrintDialog printDialog)
        {
            try
            {
                // الطريقة الأولى: طباعة عادية
                var pageSize = new Size(printDialog.PrintableAreaWidth, printDialog.PrintableAreaHeight);
                this.Measure(pageSize);
                this.Arrange(new Rect(pageSize));
                printDialog.PrintVisual(this, "توثيق الرسائل النصية - الصور المرفقة");

                MessageBox.Show("تم إرسال الصفحة للطباعة بنجاح", "طباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex) when (ex.Message.Contains("TypeConverter") || ex.Message.Contains("MarkupExtension"))
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ TypeConverter، محاولة طباعة بديلة: {ex.Message}");
                try
                {
                    // الطريقة البديلة: إنشاء حاوي بسيط
                    var container = new Border
                    {
                        Background = Brushes.White,
                        Width = 794,
                        Height = 1123,
                        Child = this.Content as FrameworkElement
                    };

                    printDialog.PrintVisual(container, "توثيق الرسائل النصية - الصور المرفقة - نسخة آمنة");
                    MessageBox.Show("تم إرسال الصفحة للطباعة بطريقة بديلة", "طباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشلت الطباعة البديلة: {ex2.Message}");
                    MessageBox.Show($"خطأ في الطباعة: {ex2.Message}\n\nجرب إعادة تشغيل البرنامج.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ الصفحة كصورة
        /// </summary>
        public void SaveAsImage(string filePath)
        {
            try
            {
                // تحديد حجم الصفحة (A4 بدقة 300 DPI)
                var pageSize = new Size(2480, 3508);
                
                // تحضير النافذة للحفظ
                this.Measure(pageSize);
                this.Arrange(new Rect(pageSize));
                
                // إنشاء RenderTargetBitmap
                var renderBitmap = new RenderTargetBitmap(
                    (int)pageSize.Width, (int)pageSize.Height, 300, 300, System.Windows.Media.PixelFormats.Pbgra32);
                
                renderBitmap.Render(this);
                
                // حفظ الصورة
                var encoder = new System.Windows.Media.Imaging.PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));
                
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    encoder.Save(fileStream);
                }
                
                MessageBox.Show($"تم حفظ الصفحة بنجاح في:\n{filePath}", "حفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
